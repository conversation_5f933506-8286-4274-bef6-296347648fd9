<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\common\model;

use app\common\model\BaseModel;
use app\common\service\FileService;

/**
 * AltAccount模型
 * Class AltAccount
 * @package app\common\model
 */
class AltAccount extends BaseModel
{

    protected $name = 'alt_account';

    /**
     * @notes 头像获取器 - 头像路径添加域名
     * @param $value
     * @return string
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function getAvatarAttr($value): string
    {
        return trim($value) ? FileService::getFileUrl($value) : '';
    }

    /**
     * @notes 关联分组
     * @return \think\model\relation\BelongsTo
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function group()
    {
        return $this->belongsTo(AltAccountGroup::class, 'group_id', 'id');
    }

    /**
     * @notes 关联运营（客服）
     * @return \think\model\relation\BelongsTo
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function operator()
    {
        return $this->belongsTo(\app\common\model\auth\Admin::class, 'operator_id', 'id');
    }

    /**
     * @notes 获取分组名称
     * @param $value
     * @param $data
     * @return string
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function getGroupNameAttr($value, $data): string
    {
        if (empty($data['group_id'])) {
            return '未分组';
        }

        $group = AltAccountGroup::findOrEmpty($data['group_id']);
        return $group->isEmpty() ? '未知分组' : $group->name;
    }

    /**
     * @notes 获取运营（客服）昵称
     * @param $value
     * @param $data
     * @return string
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function getOperatorNameAttr($value, $data): string
    {
        if (empty($data['operator_id'])) {
            return '未分配';
        }

        $operator = \app\common\model\auth\Admin::findOrEmpty($data['operator_id']);
        return $operator->isEmpty() ? '未知客服' : $operator->name;
    }
}