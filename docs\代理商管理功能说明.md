# 代理商管理功能说明

## 功能概述

代理商管理功能是基于现有管理员系统开发的专门用于管理代理商的模块。该功能复用了现有的管理员表（`la_admin`），通过角色系统来区分代理商身份，并实现了层级权限控制。

## 技术实现

### 数据表设计
- **复用现有表**：使用 `la_admin` 表存储代理商基本信息
- **角色区分**：通过 `la_admin_role` 关联表，使用角色ID=1（代理角色）来标识代理商
- **真实删除**：使用真实删除，删除后数据不可恢复
- **层级管理**：新增 `parent_id` 字段，支持管理员层级关系

### 文件结构
```
app/adminapi/
├── controller/auth/AgentController.php     # 代理商控制器
├── logic/auth/AgentLogic.php              # 代理商业务逻辑
├── lists/auth/AgentLists.php              # 代理商列表类
└── validate/auth/AgentValidate.php        # 代理商验证器

app/common/service/
└── AdminHierarchyService.php              # 管理员层级权限控制服务
```

## 功能特性

### 1. 代理商列表 (AgentLists)
- **筛选功能**：只显示具有代理角色（role_id=1）的管理员
- **搜索支持**：支持按姓名、账号模糊搜索
- **排序功能**：支持按创建时间、ID排序
- **导出功能**：支持Excel导出

### 2. 代理商管理 (AgentLogic)
- **添加代理商**：自动分配代理角色，只需基本信息
- **编辑代理商**：修改基本信息，确保始终保持代理角色
- **删除代理商**：软删除，同时清理相关缓存和token
- **详情查看**：获取代理商详细信息

### 3. 数据验证 (AgentValidate)
- **基础验证**：账号、姓名、密码等基础字段验证
- **角色验证**：确保操作的对象是代理商
- **唯一性验证**：账号唯一性检查

### 4. 层级权限控制 (AdminHierarchyService)
- **权限验证**：管理员只能操作自己的下级
- **层级查询**：递归查询所有下级管理员
- **关系验证**：防止循环引用和非法上级设置
- **缓存优化**：使用缓存提高查询性能

### 5. API接口 (AgentController)
- `GET /agent/lists` - 获取代理商列表
- `POST /agent/add` - 添加代理商
- `POST /agent/edit` - 编辑代理商
- `POST /agent/delete` - 删除代理商
- `GET /agent/detail` - 获取代理商详情

## 使用方法

### 1. 添加代理商
```php
// 请求参数
$params = [
    'name' => '代理商姓名',
    'account' => '代理商账号',
    'password' => '登录密码',
    'password_confirm' => '确认密码',
    'disable' => 0,  // 0-启用 1-禁用
    'multipoint_login' => 1,  // 0-不支持多点登录 1-支持
];
```

### 2. 编辑代理商
```php
// 请求参数
$params = [
    'id' => 1,  // 代理商ID
    'name' => '新姓名',
    'account' => '新账号',
    'password' => '新密码（可选）',
    'disable' => 0,
    'multipoint_login' => 1
];
```

### 3. 获取代理商列表
```php
// 请求参数（可选）
$params = [
    'name' => '搜索关键词',  // 按姓名搜索
    'account' => '搜索关键词',  // 按账号搜索
    'page' => 1,  // 页码
    'limit' => 25,  // 每页数量
    'sort_field' => 'create_time',  // 排序字段
    'sort_order' => 'desc'  // 排序方向
];
```

## 安全特性

1. **角色隔离**：代理商只能具有代理角色，不能分配其他角色
2. **权限控制**：所有操作都需要相应的管理权限
3. **数据验证**：严格的输入验证，防止非法数据
4. **软删除**：删除操作不会物理删除数据，可以恢复
5. **缓存清理**：修改操作会自动清理相关缓存

## 注意事项

1. **角色依赖**：系统中必须存在ID=1的"代理"角色
2. **不影响现有功能**：代理商管理不会影响现有的管理员功能
3. **数据一致性**：所有操作都使用事务处理，确保数据一致性
4. **性能考虑**：列表查询使用索引优化，支持大量数据

## 扩展建议

1. **权限细化**：可以为代理商角色配置专门的菜单权限
2. **业务数据**：可以扩展代理商相关的业务数据表
3. **统计报表**：可以添加代理商业务统计功能
4. **API接口**：可以为前端提供更多的API接口

## 核心特性

1. **角色自动分配**: 添加代理商时自动分配"代理"角色
2. **数据隔离**: 代理商数据与普通管理员数据完全隔离
3. **权限控制**: 只能操作代理商相关数据，不影响其他管理员
4. **完整CRUD**: 支持代理商的增删改查操作
5. **唯一性**: 账号在系统中必须唯一，姓名可以重复
6. **层级管理**: 支持管理员层级关系，实现上下级权限控制
7. **自动上级**: 添加代理商时自动将添加者设置为上级
8. **权限继承**: 管理员只能查看和操作自己的下级
9. **真实删除**: 删除代理商时使用真实删除，确保数据彻底清理
10. **创建权限**: 只有平台管理员才能创建代理商，确保上级关系正确

## 测试验证

功能已通过以下测试：
- ✅ 代理商添加功能
- ✅ 代理商编辑功能
- ✅ 代理商删除功能
- ✅ 代理商详情查看
- ✅ 代理商列表筛选
- ✅ 角色自动分配
- ✅ 不影响普通管理员功能
- ✅ 层级权限控制
- ✅ 自动上级设置
- ✅ 权限验证机制
- ✅ 真实删除功能
- ✅ 平台管理员创建权限控制

## 技术支持

如有问题，请检查：
1. 数据库中是否存在ID=1的"代理"角色
2. 相关表结构是否完整
3. 权限配置是否正确
4. 缓存是否需要清理
