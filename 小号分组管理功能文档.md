# 小号分组管理功能文档

## 功能概述

为AltAccount小号管理系统新增分组管理功能，支持对小号进行分组管理，提高管理效率。

### 核心特性
- **分组管理**: 支持创建、编辑、删除分组
- **批量操作**: 支持批量为小号设置分组
- **权限控制**: 租户只能管理自己创建的分组和小号
- **数据关联**: 一个小号只能属于一个分组，一个分组可以包含多个小号

## 数据库设计

### 1. 新增分组表 `la_alt_account_group`

```sql
CREATE TABLE `la_alt_account_group` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` int(11) DEFAULT NULL COMMENT '租户ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '分组名称',
  `description` varchar(255) DEFAULT '' COMMENT '分组描述',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(10) unsigned DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE,
  KEY `idx_name` (`name`) USING BTREE,
  UNIQUE KEY `uk_tenant_name` (`tenant_id`, `name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小号分组表';
```

### 2. 修改小号表 `la_alt_account`

```sql
-- 添加分组ID字段
ALTER TABLE `la_alt_account` ADD COLUMN `group_id` int(11) DEFAULT NULL COMMENT '分组ID' AFTER `tenant_id`;

-- 添加索引
ALTER TABLE `la_alt_account` ADD KEY `idx_group_id` (`group_id`) USING BTREE;
```

## API接口文档

### 分组管理接口

#### 1. 获取分组列表
- **接口**: `GET /adminapi/alt_account_group/lists`
- **权限**: `alt_account_group/lists`
- **参数**: 
  ```json
  {
    "page": 1,
    "limit": 20,
    "name": "分组名称搜索",
    "description": "描述搜索"
  }
  ```
- **返回**: 分组列表，包含每个分组的小号数量

#### 2. 添加分组
- **接口**: `POST /adminapi/alt_account_group/add`
- **权限**: `alt_account_group/add`
- **参数**:
  ```json
  {
    "name": "分组名称",
    "description": "分组描述"
  }
  ```

#### 3. 编辑分组
- **接口**: `POST /adminapi/alt_account_group/edit`
- **权限**: `alt_account_group/edit`
- **参数**:
  ```json
  {
    "id": 1,
    "name": "新分组名称",
    "description": "新分组描述"
  }
  ```

#### 4. 删除分组
- **接口**: `POST /adminapi/alt_account_group/delete`
- **权限**: `alt_account_group/delete`
- **参数**:
  ```json
  {
    "id": 1
  }
  ```
- **说明**: 删除分组时，该分组下的小号会自动设置为"未分组"状态

#### 5. 获取分组详情
- **接口**: `GET /adminapi/alt_account_group/detail`
- **权限**: `alt_account_group/detail`
- **参数**:
  ```json
  {
    "id": 1
  }
  ```

#### 6. 获取分组选项列表
- **接口**: `GET /adminapi/alt_account_group/getGroupOptions`
- **权限**: `alt_account_group/getGroupOptions`
- **返回**: 用于下拉选择的分组列表，包含"未分组"选项

### 小号分组操作接口

#### 批量设置小号分组
- **接口**: `POST /adminapi/alt_account/batchSetGroup`
- **权限**: `alt_account/batchSetGroup`
- **参数**:
  ```json
  {
    "alt_account_ids": [1, 2, 3],
    "group_id": 5
  }
  ```
- **说明**: 
  - `group_id` 为 0 表示设置为"未分组"
  - 支持批量操作多个小号
  - 如果小号已有分组，会自动转移到新分组

## 权限标识

### 分组管理权限
- `alt_account_group/lists` - 查看分组列表
- `alt_account_group/add` - 添加分组
- `alt_account_group/edit` - 编辑分组
- `alt_account_group/delete` - 删除分组
- `alt_account_group/detail` - 查看分组详情
- `alt_account_group/getGroupOptions` - 获取分组选项

### 小号分组操作权限
- `alt_account/batchSetGroup` - 批量设置小号分组

## 实现文件结构

```
app/
├── adminapi/
│   ├── controller/
│   │   ├── AltAccountGroupController.php     # 分组管理控制器
│   │   └── AltAccountController.php          # 小号控制器（新增批量设置分组方法）
│   ├── logic/
│   │   ├── AltAccountGroupLogic.php          # 分组业务逻辑
│   │   └── AltAccountLogic.php               # 小号业务逻辑（新增批量设置分组方法）
│   ├── lists/
│   │   ├── AltAccountGroupLists.php          # 分组列表类
│   │   └── AltAccountLists.php               # 小号列表类（显示分组信息）
│   └── validate/
│       ├── AltAccountGroupValidate.php       # 分组验证器
│       └── AltAccountValidate.php            # 小号验证器（新增批量设置分组验证）
└── common/
    └── model/
        ├── AltAccountGroup.php               # 分组模型
        └── AltAccount.php                    # 小号模型（新增分组关联）
```

## 权限控制机制

### 1. 租户隔离
- 分组创建时自动设置 `tenant_id` 为当前用户ID
- 租户只能查看和操作自己创建的分组
- 只能为自己的小号设置分组

### 2. 数据验证
- 分组名称在同一租户下必须唯一
- 批量操作时验证所有小号的归属权
- 验证目标分组的归属权

### 3. 业务规则
- 删除分组时自动将该分组下的小号设置为未分组
- 支持将小号设置为未分组状态（group_id = null）
- 使用事务确保数据一致性

## 前端开发指南

### 1. 分组管理页面
- 分组列表展示：名称、描述、小号数量、创建时间
- 支持搜索：按分组名称和描述搜索
- 操作按钮：添加、编辑、删除分组

### 2. 小号列表页面
- 新增分组列显示小号所属分组
- 批量操作：选中多个小号后可批量设置分组
- 分组筛选：支持按分组筛选小号列表

### 3. 分组选择组件
- 下拉选择框，包含所有分组和"未分组"选项
- 用于批量设置分组和筛选功能

## 使用流程

1. **创建分组**: 管理员创建分组，设置名称和描述
2. **分配小号**: 选择一个或多个小号，批量设置到指定分组
3. **管理分组**: 可以编辑分组信息或删除不需要的分组
4. **查看统计**: 在分组列表中查看每个分组包含的小号数量

## 技术特点

- ✅ 遵循likeadmin项目编码规范
- ✅ 完整的权限控制和数据隔离
- ✅ 支持批量操作和事务处理
- ✅ 标准的JSON响应格式
- ✅ 完整的错误处理机制
- ✅ 物理删除（根据项目配置）
