# 管理员层级权限控制系统

## 系统概述

本系统实现了完整的管理员层级权限控制机制，通过角色和上下级关系，确保每个管理员只能在自己的权限范围内操作，实现了严格的数据隔离和权限边界控制。

## 角色层级结构

```
Root用户 (超级管理员)
    ↓
平台管理员 (角色ID: 5)
    ↓
代理商 (角色ID: 1)
    ↓
租户 (角色ID: 2)
```

## 权限控制规则

### 1. 创建权限
- **Root用户**: 可以创建任何角色的管理员
- **平台管理员**: 只能创建代理商
- **代理商**: 只能创建租户
- **租户**: 无法创建其他管理员

### 2. 管理权限
- **查看权限**: 管理员只能查看自己和自己的下级
- **编辑权限**: 管理员只能编辑自己的下级
- **删除权限**: 管理员只能删除自己的下级（且该下级没有下级）
- **Root特权**: Root用户可以操作所有管理员

### 3. 上级关系
- **自动设置**: 创建管理员时，自动将创建者设置为新管理员的上级
- **Root例外**: Root用户创建的管理员可以是顶级（parent_id = 0）
- **不可修改**: 上级关系一旦确定，无法通过编辑接口修改
- **循环防护**: 系统防止设置循环引用的上级关系

## 核心服务类

### 1. AdminHierarchyService (层级权限控制)
```php
// 获取所有下级ID
AdminHierarchyService::getSubordinateIds($adminId);

// 权限验证
AdminHierarchyService::hasPermission($operatorId, $targetId);

// 获取可查看的管理员ID
AdminHierarchyService::getViewableAdminIds($adminId);

// 验证上级关系
AdminHierarchyService::validateParentRelation($adminId, $parentId, $currentUserId);
```

### 2. PlatformAdminService (平台管理员权限)
```php
// 检查是否为平台管理员
PlatformAdminService::isPlatformAdmin($adminId);

// 验证平台管理员权限
PlatformAdminService::validatePlatformAdmin($adminId, '操作名称');
```

### 3. AgentAdminService (代理商权限)
```php
// 检查是否为代理商
AgentAdminService::isAgent($adminId);

// 验证代理商权限
AgentAdminService::validateAgent($adminId, '操作名称');
```

## 数据库设计

### 核心字段
- `parent_id`: 上级管理员ID，0表示顶级
- `root`: 是否为超级管理员，1表示是
- `role_id`: 通过 `la_admin_role` 表关联角色

### 关键表结构
```sql
-- 管理员表
la_admin
├── id (主键)
├── parent_id (上级ID)
├── root (是否超级管理员)
└── ... (其他字段)

-- 角色关联表
la_admin_role
├── admin_id (管理员ID)
└── role_id (角色ID)

-- 角色表
la_system_role
├── id (角色ID)
├── name (角色名称)
└── desc (角色描述)
```

## 权限验证流程

### 1. 创建流程
```
1. 验证创建者角色权限
2. 设置上级关系
3. 验证上级关系合法性
4. 创建管理员记录
5. 分配对应角色
6. 清除权限缓存
```

### 2. 操作流程
```
1. 获取当前操作者ID
2. 获取目标管理员ID
3. 验证操作权限
4. 执行具体操作
5. 更新相关缓存
```

### 3. 查询流程
```
1. 获取当前管理员ID
2. 查询可查看的管理员ID列表
3. 应用权限过滤条件
4. 返回过滤后的数据
```

## 安全机制

### 1. 权限边界
- **严格验证**: 每个操作都进行权限验证
- **数据隔离**: 不同层级的数据完全隔离
- **防止越权**: 多重检查防止权限绕过

### 2. 数据保护
- **Root保护**: Root用户不能被删除或降级
- **层级保护**: 有下级的管理员不能被删除
- **循环防护**: 防止设置循环引用的上级关系

### 3. 缓存机制
- **性能优化**: 使用缓存提高查询效率
- **自动清理**: 数据变更时自动清除相关缓存
- **一致性保证**: 确保缓存与数据库数据一致

## 实际应用场景

### 1. 代理商管理
- 平台管理员创建代理商
- 代理商管理自己的下级代理商
- 层级分润和业务管理

### 2. 租户管理
- 代理商创建租户
- 租户数据隔离
- 多租户业务支持

### 3. 权限管控
- 数据访问控制
- 操作权限限制
- 业务边界划分

## 扩展能力

### 1. 角色扩展
- 支持添加新的角色类型
- 灵活的权限配置
- 自定义权限规则

### 2. 层级扩展
- 支持无限层级深度
- 动态权限继承
- 复杂组织架构支持

### 3. 功能扩展
- 批量操作支持
- 权限委托机制
- 临时权限授权

## 性能优化

### 1. 查询优化
- 使用索引优化查询
- 缓存热点数据
- 减少递归查询

### 2. 缓存策略
- 分层缓存设计
- 智能缓存更新
- 缓存预热机制

### 3. 数据库优化
- 合理的表结构设计
- 高效的查询语句
- 适当的数据冗余

## 监控和维护

### 1. 权限审计
- 操作日志记录
- 权限变更追踪
- 异常行为监控

### 2. 数据一致性
- 定期数据校验
- 孤儿数据清理
- 权限关系修复

### 3. 性能监控
- 查询性能监控
- 缓存命中率统计
- 系统负载分析

## 最佳实践

### 1. 权限设计
- 最小权限原则
- 明确的权限边界
- 合理的角色划分

### 2. 数据管理
- 规范的数据操作
- 完整的事务处理
- 及时的缓存更新

### 3. 安全考虑
- 输入数据验证
- 权限二次确认
- 敏感操作审计

管理员层级权限控制系统为业务提供了强大而灵活的权限管理能力，确保了系统的安全性、可扩展性和可维护性。
