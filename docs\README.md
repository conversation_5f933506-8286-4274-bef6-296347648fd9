# 管理员层级权限系统文档

本目录包含了管理员层级权限系统的完整文档。

## 文档结构

### 功能说明文档
- [代理商管理功能说明](代理商管理功能说明.md) - 代理商管理功能的详细说明
- [租户管理功能说明](租户管理功能说明.md) - 租户管理功能的详细说明
- [层级权限控制说明](层级权限控制说明.md) - 整体层级权限控制机制说明

### API接口文档
- [代理商管理API文档](代理商管理API文档.md) - 代理商管理相关API接口
- [租户管理API文档](租户管理API文档.md) - 租户管理相关API接口

### 技术文档
- [数据库设计文档](数据库设计文档.md) - 数据库表结构和字段说明
- [权限控制架构文档](权限控制架构文档.md) - 权限控制的技术架构

## 系统概述

本系统实现了完整的管理员层级权限控制，包括：

### 角色层级
```
Root用户
    ↓
平台管理员 (角色ID: 5)
    ↓
代理商 (角色ID: 1)
    ↓
租户 (角色ID: 2)
```

### 权限规则
1. **创建权限**：
   - 平台管理员可以创建代理商
   - 代理商可以创建租户
   - Root用户可以创建任何角色

2. **管理权限**：
   - 管理员只能查看和操作自己的下级
   - 支持多级层级管理
   - 真实删除，数据不可恢复

3. **数据隔离**：
   - 严格的数据权限控制
   - 防止越权访问
   - 自动上级关系设置

## 快速开始

1. 查看对应的功能说明文档了解业务逻辑
2. 参考API文档进行接口调用
3. 查看技术文档了解实现细节

## 更新日志

- 2024/08/24: 初始版本，实现代理商管理和层级权限控制
- 2024/08/24: 添加租户管理功能
- 2024/08/24: 完善文档结构和组织
