-- 小号分组管理权限配置SQL
-- 注意：需要根据实际的菜单ID调整 @parent_id 的值

-- 假设小号管理的菜单ID为 @parent_id，需要根据实际情况修改
-- SET @parent_id = (SELECT id FROM la_system_menu WHERE perms = 'alt_account/lists' AND type = 'C');

-- 1. 分组管理菜单
INSERT INTO `la_system_menu`(`pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`)
VALUES (@parent_id, 'C', '分组管理', '', 2, 'alt_account_group/lists', 'alt_account_group', 'alt_account_group/index', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 获取分组管理菜单ID
SELECT @group_menu_id := LAST_INSERT_ID();

-- 2. 分组管理按钮权限
INSERT INTO `la_system_menu`(`pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`)
VALUES 
(@group_menu_id, 'A', '添加分组', '', 1, 'alt_account_group/add', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@group_menu_id, 'A', '编辑分组', '', 2, 'alt_account_group/edit', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@group_menu_id, 'A', '删除分组', '', 3, 'alt_account_group/delete', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@group_menu_id, 'A', '分组详情', '', 4, 'alt_account_group/detail', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(@group_menu_id, 'A', '获取分组选项', '', 5, 'alt_account_group/getGroupOptions', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 3. 小号管理新增权限（批量设置分组）
INSERT INTO `la_system_menu`(`pid`, `type`, `name`, `icon`, `sort`, `perms`, `paths`, `component`, `selected`, `params`, `is_cache`, `is_show`, `is_disable`, `create_time`, `update_time`)
VALUES (@parent_id, 'A', '批量设置分组', '', 10, 'alt_account/batchSetGroup', '', '', '', '', 0, 1, 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 权限标识列表：
-- 分组管理权限：
-- - alt_account_group/lists          查看分组列表
-- - alt_account_group/add            添加分组
-- - alt_account_group/edit           编辑分组
-- - alt_account_group/delete         删除分组
-- - alt_account_group/detail         查看分组详情
-- - alt_account_group/getGroupOptions 获取分组选项

-- 小号分组操作权限：
-- - alt_account/batchSetGroup        批量设置小号分组
