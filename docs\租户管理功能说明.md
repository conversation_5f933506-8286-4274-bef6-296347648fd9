# 租户管理功能说明

## 功能概述

租户管理功能是基于likeadmin管理系统开发的扩展功能，专门用于管理租户账户。该功能在现有管理员系统基础上，通过角色区分实现租户的专门管理，并支持层级权限控制。

租户管理功能是基于现有管理员系统开发的专门用于管理租户的模块。该功能复用了现有的管理员表（`la_admin`），通过角色系统来区分租户身份，并实现了层级权限控制。

## 设计特点

- **复用现有表**：使用 `la_admin` 表存储租户基本信息
- **角色区分**：通过 `la_admin_role` 关联表，使用角色ID=2（租户角色）来标识租户
- **真实删除**：使用真实删除，删除后数据不可恢复
- **层级管理**：新增 `parent_id` 字段，支持管理员层级关系

## 文件结构

```
app/adminapi/
├── controller/auth/TenantController.php     # 租户控制器
├── logic/auth/TenantLogic.php              # 租户业务逻辑
├── lists/auth/TenantLists.php              # 租户列表类
└── validate/auth/TenantValidate.php        # 租户验证器

app/common/service/
└── AgentAdminService.php                   # 代理商权限控制服务
```

## 核心功能模块

### 1. 数据管理 (TenantLogic)
- **添加租户**：创建新的租户账号，自动分配租户角色
- **编辑租户**：修改租户基本信息，支持密码修改
- **删除租户**：真实删除租户数据，清理关联信息
- **查看详情**：获取租户详细信息

### 2. 列表展示 (TenantLists)
- **分页列表**：支持分页显示租户列表
- **搜索筛选**：支持按姓名、账号搜索
- **排序功能**：支持按创建时间、ID排序
- **导出功能**：支持导出租户列表到Excel

### 3. 数据验证 (TenantValidate)
- **基础验证**：账号、姓名、密码等基础字段验证
- **角色验证**：确保操作的对象是租户
- **唯一性验证**：账号唯一性检查

### 4. 权限控制 (AgentAdminService)
- **权限验证**：只有代理商才能创建租户
- **层级查询**：递归查询所有下级管理员
- **关系验证**：防止循环引用和非法上级设置

### 5. API接口 (TenantController)
- **RESTful设计**：标准的增删改查接口
- **权限集成**：集成层级权限控制
- **异常处理**：统一的错误处理机制

## 业务流程

### 租户创建流程
1. 验证操作者是否为代理商
2. 验证租户基本信息
3. 设置上级关系（创建者为上级）
4. 创建租户账号
5. 分配租户角色
6. 清除权限缓存

### 租户编辑流程
1. 验证操作权限（只能编辑下级）
2. 验证修改信息
3. 更新租户数据
4. 处理密码修改
5. 处理禁用状态
6. 重新分配角色

### 租户删除流程
1. 验证操作权限
2. 检查是否有下级
3. 真实删除租户记录
4. 清理Token和缓存
5. 删除角色关联
6. 清除权限缓存

## 权限控制机制

### 创建权限
- 只有代理商角色才能创建租户
- root用户也可以创建租户
- 平台管理员无法创建租户

### 管理权限
- 管理员只能查看和操作自己的下级租户
- 支持多级层级管理
- 自动上级关系设置

### 数据隔离
- 严格的数据权限控制
- 防止越权访问
- 层级权限继承

## 核心特性

1. **角色自动分配**: 添加租户时自动分配"租户"角色
2. **数据隔离**: 租户数据与其他管理员数据完全隔离
3. **权限控制**: 只能操作租户相关数据，不影响其他管理员
4. **完整CRUD**: 支持租户的增删改查操作
5. **唯一性**: 账号在系统中必须唯一，姓名可以重复
6. **层级管理**: 支持管理员层级关系，实现上下级权限控制
7. **自动上级**: 添加租户时自动将添加者设置为上级
8. **权限继承**: 管理员只能查看和操作自己的下级
9. **真实删除**: 删除租户时使用真实删除，确保数据彻底清理
10. **创建权限**: 只有代理商才能创建租户，确保上级关系正确

## 扩展功能

1. **导出功能**：支持租户列表导出
2. **搜索功能**：支持多字段模糊搜索
3. **排序功能**：支持多字段排序
4. **批量操作**：可扩展批量启用/禁用功能

## 测试验证

- ✅ 租户添加功能
- ✅ 租户编辑功能  
- ✅ 租户删除功能
- ✅ 租户详情查看
- ✅ 租户列表筛选
- ✅ 角色自动分配
- ✅ 不影响其他管理员功能
- ✅ 层级权限控制
- ✅ 自动上级设置
- ✅ 权限验证机制
- ✅ 真实删除功能
- ✅ 代理商创建权限控制

## 注意事项

1. **权限边界**：严格按照层级权限控制，不能越权操作
2. **数据安全**：真实删除不可恢复，操作需谨慎
3. **角色一致性**：确保租户始终具有租户角色
4. **上级关系**：租户的上级关系一旦确定不可修改
5. **账号唯一性**：账号在全系统范围内必须唯一

## 技术要点

1. **复用设计**：最大化复用现有代码和数据结构
2. **权限集成**：无缝集成层级权限控制系统
3. **性能优化**：使用缓存提高权限查询效率
4. **异常处理**：完善的异常处理和错误提示
5. **代码规范**：遵循项目编码规范和注释要求

租户管理功能为系统提供了完整的租户管理能力，通过层级权限控制确保了数据安全和操作规范，是管理员层级权限系统的重要组成部分。
