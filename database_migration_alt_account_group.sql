-- 小号分组管理数据库迁移脚本
-- 创建时间: 2025-08-24

-- 1. 创建小号分组表
CREATE TABLE `la_alt_account_group` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` int(11) DEFAULT NULL COMMENT '租户ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '分组名称',
  `description` varchar(255) DEFAULT '' COMMENT '分组描述',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `delete_time` int(10) unsigned DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_tenant_id` (`tenant_id`) USING BTREE COMMENT '租户ID索引',
  KEY `idx_name` (`name`) USING BTREE COMMENT '分组名称索引',
  UNIQUE KEY `uk_tenant_name` (`tenant_id`, `name`) USING BTREE COMMENT '租户下分组名称唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小号分组表';

-- 2. 为 la_alt_account 表添加 group_id 字段
ALTER TABLE `la_alt_account` ADD COLUMN `group_id` int(11) DEFAULT NULL COMMENT '分组ID' AFTER `tenant_id`;

-- 3. 为 la_alt_account 表的 group_id 字段添加索引
ALTER TABLE `la_alt_account` ADD KEY `idx_group_id` (`group_id`) USING BTREE COMMENT '分组ID索引';

-- 4. 插入默认分组数据（可选）
-- INSERT INTO `la_alt_account_group` (`tenant_id`, `name`, `description`, `create_time`, `update_time`) 
-- VALUES (1, '默认分组', '系统默认分组', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 注意事项：
-- 1. tenant_id 和 name 组合唯一，确保同一租户下分组名称不重复
-- 2. group_id 为 NULL 表示小号未分组
-- 3. 删除分组时需要先处理该分组下的小号（设置为未分组或转移到其他分组）
-- 4. 根据项目配置，可能不使用软删除功能
