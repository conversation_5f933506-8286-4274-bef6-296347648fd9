<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------


namespace app\adminapi\controller;


use app\adminapi\controller\BaseAdminController;
use app\adminapi\lists\AltAccountLists;
use app\adminapi\logic\AltAccountLogic;
use app\adminapi\validate\AltAccountValidate;


/**
 * AltAccount控制器
 * Class AltAccountController
 * @package app\adminapi\controller
 */
class AltAccountController extends BaseAdminController
{


    /**
     * @notes 获取列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function lists()
    {
        return $this->dataLists(new AltAccountLists($this->adminId));
    }


    /**
     * @notes 添加
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function add()
    {
        $params = (new AltAccountValidate())->post()->goCheck('add');
        $result = AltAccountLogic::add($params, $this->adminId);
        if (true === $result) {
            return $this->success('添加成功', [], 1, 1);
        }
        return $this->fail(AltAccountLogic::getError());
    }


    /**
     * @notes 编辑
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function edit()
    {
        $params = (new AltAccountValidate())->post()->goCheck('edit');
        $result = AltAccountLogic::edit($params, $this->adminId);
        if (true === $result) {
            return $this->success('编辑成功', [], 1, 1);
        }
        return $this->fail(AltAccountLogic::getError());
    }


    /**
     * @notes 删除
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function delete()
    {
        $params = (new AltAccountValidate())->post()->goCheck('delete');
        $result = AltAccountLogic::delete($params, $this->adminId);
        if (true === $result) {
            return $this->success('删除成功', [], 1, 1);
        }
        return $this->fail(AltAccountLogic::getError());
    }


    /**
     * @notes 获取详情
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function detail()
    {
        $params = (new AltAccountValidate())->goCheck('detail');
        $result = AltAccountLogic::detail($params, $this->adminId);
        return $this->data($result);
    }


    /**
     * @notes 分配客服（运营）
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function assignCustomerService()
    {
        $params = (new AltAccountValidate())->post()->goCheck('assignCustomerService');
        $result = AltAccountLogic::assignCustomerService($params, $this->adminId);
        if (true === $result) {
            return $this->success('分配客服成功', [], 1, 1);
        }
        return $this->fail(AltAccountLogic::getError());
    }


    /**
     * @notes 获取可分配的运营列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function getAvailableOperators()
    {
        $result = AltAccountLogic::getAvailableOperators($this->adminId);
        return $this->data($result);
    }


    /**
     * @notes 批量设置小号分组
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function batchSetGroup()
    {
        $params = (new AltAccountValidate())->post()->goCheck('batchSetGroup');
        $result = AltAccountLogic::batchSetGroup($params, $this->adminId);
        if (true === $result) {
            return $this->success('设置分组成功', [], 1, 1);
        }
        return $this->fail(AltAccountLogic::getError());
    }


}