<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\validate;


use app\common\validate\BaseValidate;


/**
 * AltAccount验证器
 * Class AltAccountValidate
 * @package app\adminapi\validate
 */
class AltAccountValidate extends BaseValidate
{

     /**
      * 设置校验规则
      * @var string[]
      */
    protected $rule = [
        'id' => 'require',
        'area_code' => 'require',
        'phone' => 'require',
        'password' => 'require',
        'mid' => 'require',
        'accesstoken' => 'require',
        'refreshtoken' => 'require',
    ];


    /**
     * 参数描述
     * @var string[]
     */
    protected $field = [
        'id' => 'id',
        'area_code' => '区号',
        'phone' => '电话',
        'password' => '密码',
        'mid' => '自定义ID',
        'accesstoken' => 'accesstoken',
        'refreshtoken' => 'refreshtoken',
    ];


    /**
     * @notes 添加场景
     * @return AltAccountValidate
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function sceneAdd()
    {
        return $this->only(['area_code','phone','password','mid','accesstoken','refreshtoken']);
    }


    /**
     * @notes 编辑场景
     * @return AltAccountValidate
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function sceneEdit()
    {
        return $this->only(['id','area_code','phone','password','mid','accesstoken','refreshtoken']);
    }


    /**
     * @notes 删除场景
     * @return AltAccountValidate
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function sceneDelete()
    {
        return $this->only(['id']);
    }


    /**
     * @notes 详情场景
     * @return AltAccountValidate
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public function sceneDetail()
    {
        return $this->only(['id']);
    }

}