<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\logic;


use app\adminapi\logic\auth\OperatorLogic;
use app\common\model\AltAccount;
use app\common\model\auth\Admin;
use app\common\model\auth\AdminRole;
use app\common\logic\BaseLogic;
use app\common\service\AdminHierarchyService;
use think\facade\Db;


/**
 * AltAccount逻辑
 * Class AltAccountLogic
 * @package app\adminapi\logic
 */
class AltAccountLogic extends BaseLogic
{


    /**
     * @notes 添加
     * @param array $params
     * @param int $currentAdminId 当前操作的管理员ID
     * @return bool
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function add(array $params, int $currentAdminId = 0): bool
    {
        Db::startTrans();
        try {
            // 设置默认头像
            $defaultAvatar = config('project.default_image.user_avatar');
            $avatar = !empty($params['avatar']) ? $params['avatar'] : $defaultAvatar;

            AltAccount::create([
                'tenant_id' => $currentAdminId, // 自动设置tenant_id为当前用户ID
                'avatar' => $avatar, // 自动填充默认头像
                'area_code' => $params['area_code'],
                'phone' => $params['phone'],
                'password' => $params['password'],
                'mid' => $params['mid'],
                'accesstoken' => $params['accesstoken'],
                'refreshtoken' => $params['refreshtoken'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 编辑
     * @param array $params
     * @param int $currentAdminId 当前操作的管理员ID
     * @return bool
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function edit(array $params, int $currentAdminId = 0): bool
    {
        Db::startTrans();
        try {
            // 权限验证：检查小号是否属于当前租户
            $altAccount = AltAccount::findOrEmpty($params['id']);
            if ($altAccount->isEmpty()) {
                throw new \Exception('小号不存在');
            }

            if ($altAccount->tenant_id != $currentAdminId) {
                throw new \Exception('您没有权限编辑该小号');
            }

            AltAccount::where('id', $params['id'])->update([
                'avatar' => $params['avatar'],
                'nickname' => $params['nickname'],
                'area_code' => $params['area_code'],
                'phone' => $params['phone'],
                'password' => $params['password'],
                'mid' => $params['mid'],
                'accesstoken' => $params['accesstoken'],
                'refreshtoken' => $params['refreshtoken'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 删除
     * @param array $params
     * @param int $currentAdminId 当前操作的管理员ID
     * @return bool
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function delete(array $params, int $currentAdminId = 0): bool
    {
        try {
            // 权限验证：检查小号是否属于当前租户
            $altAccount = AltAccount::findOrEmpty($params['id']);
            if ($altAccount->isEmpty()) {
                self::setError('小号不存在');
                return false;
            }

            if ($altAccount->tenant_id != $currentAdminId) {
                self::setError('您没有权限删除该小号');
                return false;
            }

            return AltAccount::destroy($params['id']);
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 获取详情
     * @param $params
     * @param int $currentAdminId 当前操作的管理员ID
     * @return array
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function detail($params, int $currentAdminId = 0): array
    {
        // 权限验证：检查小号是否属于当前租户
        $altAccount = AltAccount::findOrEmpty($params['id']);
        if ($altAccount->isEmpty()) {
            throw new \Exception('小号不存在');
        }

        if ($altAccount->tenant_id != $currentAdminId) {
            throw new \Exception('您没有权限查看该小号详情');
        }

        return $altAccount->toArray();
    }


    /**
     * @notes 分配客服（运营）
     * @param array $params
     * @param int $currentAdminId 当前操作的管理员ID
     * @return bool
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function assignCustomerService(array $params, int $currentAdminId = 0): bool
    {
        Db::startTrans();
        try {
            $altAccountIds = $params['alt_account_ids'];
            $operatorId = $params['operator_id'];

            // 验证运营是否为当前租户的下级
            if (!AdminHierarchyService::hasPermission($currentAdminId, $operatorId)) {
                throw new \Exception('您没有权限将小号分配给该运营人员');
            }

            // 验证运营是否具有运营角色
            $hasOperatorRole = AdminRole::where('admin_id', $operatorId)
                ->where('role_id', OperatorLogic::$operatorRoleId)
                ->find();
            if (!$hasOperatorRole) {
                throw new \Exception('选择的人员不是运营角色');
            }

            // 验证所有小号是否属于当前租户
            foreach ($altAccountIds as $altAccountId) {
                $altAccount = AltAccount::findOrEmpty($altAccountId);
                if ($altAccount->isEmpty()) {
                    throw new \Exception("小号ID {$altAccountId} 不存在");
                }

                if ($altAccount->tenant_id != $currentAdminId) {
                    throw new \Exception("您没有权限操作小号ID {$altAccountId}");
                }
            }

            // 批量更新小号的运营分配
            AltAccount::where('id', 'in', $altAccountIds)->update([
                'operator_id' => $operatorId
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 获取可分配的运营列表
     * @param int $currentAdminId 当前操作的管理员ID
     * @return array
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function getAvailableOperators(int $currentAdminId = 0): array
    {
        try {
            // 获取当前管理员的下级ID列表
            $subordinateIds = AdminHierarchyService::getSubordinateIds($currentAdminId);

            if (empty($subordinateIds)) {
                return [];
            }

            // 获取具有运营角色的管理员ID
            $operatorAdminIds = AdminRole::where('role_id', OperatorLogic::$operatorRoleId)
                ->column('admin_id');

            // 取交集：既是下级，又是运营角色
            $availableOperatorIds = array_intersect($subordinateIds, $operatorAdminIds);

            if (empty($availableOperatorIds)) {
                return [];
            }

            // 查询运营信息
            return Admin::where('id', 'in', $availableOperatorIds)
                ->where('disable', 0) // 只查询启用的运营
                ->field(['id', 'name', 'account', 'avatar'])
                ->order('id', 'asc')
                ->select()
                ->toArray();
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return [];
        }
    }


    /**
     * @notes 批量设置小号分组
     * @param array $params
     * @param int $currentAdminId 当前操作的管理员ID
     * @return bool
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function batchSetGroup(array $params, int $currentAdminId = 0): bool
    {
        Db::startTrans();
        try {
            $altAccountIds = $params['alt_account_ids'];
            $groupId = $params['group_id'];

            // 如果设置分组ID不为0，验证分组是否属于当前租户
            if ($groupId > 0) {
                $group = \app\common\model\AltAccountGroup::findOrEmpty($groupId);
                if ($group->isEmpty()) {
                    throw new \Exception('目标分组不存在');
                }

                if ($group->tenant_id != $currentAdminId) {
                    throw new \Exception('您没有权限将小号分配到该分组');
                }
            }

            // 验证所有小号是否属于当前租户
            foreach ($altAccountIds as $altAccountId) {
                $altAccount = AltAccount::findOrEmpty($altAccountId);
                if ($altAccount->isEmpty()) {
                    throw new \Exception("小号ID {$altAccountId} 不存在");
                }

                if ($altAccount->tenant_id != $currentAdminId) {
                    throw new \Exception("您没有权限操作小号ID {$altAccountId}");
                }
            }

            // 批量更新小号的分组
            $updateData = ['group_id' => $groupId > 0 ? $groupId : null];
            AltAccount::where('id', 'in', $altAccountIds)->update($updateData);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }
}