<?php
// +----------------------------------------------------------------------
// | likeadmin快速开发前后端分离管理后台（PHP版）
// +----------------------------------------------------------------------
// | 欢迎阅读学习系统程序代码，建议反馈是我们前进的动力
// | 开源版本可自由商用，可去除界面版权logo
// | gitee下载：https://gitee.com/likeshop_gitee/likeadmin
// | github下载：https://github.com/likeshop-github/likeadmin
// | 访问官网：https://www.likeadmin.cn
// | likeadmin团队 版权所有 拥有最终解释权
// +----------------------------------------------------------------------
// | author: likeadminTeam
// +----------------------------------------------------------------------

namespace app\adminapi\logic;


use app\common\model\AltAccount;
use app\common\logic\BaseLogic;
use think\facade\Db;


/**
 * AltAccount逻辑
 * Class AltAccountLogic
 * @package app\adminapi\logic
 */
class AltAccountLogic extends BaseLogic
{


    /**
     * @notes 添加
     * @param array $params
     * @param int $currentAdminId 当前操作的管理员ID
     * @return bool
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function add(array $params, int $currentAdminId = 0): bool
    {
        Db::startTrans();
        try {
            AltAccount::create([
                'tenant_id' => $currentAdminId, // 自动设置tenant_id为当前用户ID
                'area_code' => $params['area_code'],
                'phone' => $params['phone'],
                'password' => $params['password'],
                'mid' => $params['mid'],
                'accesstoken' => $params['accesstoken'],
                'refreshtoken' => $params['refreshtoken'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 编辑
     * @param array $params
     * @param int $currentAdminId 当前操作的管理员ID
     * @return bool
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function edit(array $params, int $currentAdminId = 0): bool
    {
        Db::startTrans();
        try {
            // 权限验证：检查小号是否属于当前租户
            $altAccount = AltAccount::findOrEmpty($params['id']);
            if ($altAccount->isEmpty()) {
                throw new \Exception('小号不存在');
            }

            if ($altAccount->tenant_id != $currentAdminId) {
                throw new \Exception('您没有权限编辑该小号');
            }

            AltAccount::where('id', $params['id'])->update([
                'avatar' => $params['avatar'],
                'nickname' => $params['nickname'],
                'area_code' => $params['area_code'],
                'phone' => $params['phone'],
                'password' => $params['password'],
                'mid' => $params['mid'],
                'accesstoken' => $params['accesstoken'],
                'refreshtoken' => $params['refreshtoken'],
            ]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 删除
     * @param array $params
     * @param int $currentAdminId 当前操作的管理员ID
     * @return bool
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function delete(array $params, int $currentAdminId = 0): bool
    {
        try {
            // 权限验证：检查小号是否属于当前租户
            $altAccount = AltAccount::findOrEmpty($params['id']);
            if ($altAccount->isEmpty()) {
                self::setError('小号不存在');
                return false;
            }

            if ($altAccount->tenant_id != $currentAdminId) {
                self::setError('您没有权限删除该小号');
                return false;
            }

            return AltAccount::destroy($params['id']);
        } catch (\Exception $e) {
            self::setError($e->getMessage());
            return false;
        }
    }


    /**
     * @notes 获取详情
     * @param $params
     * @param int $currentAdminId 当前操作的管理员ID
     * @return array
     * <AUTHOR>
     * @date 2025/08/24 23:53
     */
    public static function detail($params, int $currentAdminId = 0): array
    {
        // 权限验证：检查小号是否属于当前租户
        $altAccount = AltAccount::findOrEmpty($params['id']);
        if ($altAccount->isEmpty()) {
            throw new \Exception('小号不存在');
        }

        if ($altAccount->tenant_id != $currentAdminId) {
            throw new \Exception('您没有权限查看该小号详情');
        }

        return $altAccount->toArray();
    }
}