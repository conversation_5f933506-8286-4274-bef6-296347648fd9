# 运营管理功能说明

## 功能概述

运营管理功能是基于现有管理员系统开发的专门用于管理运营人员的模块。该功能复用了现有的管理员表（`la_admin`），通过角色系统来区分运营身份，并实现了层级权限控制。

运营管理功能是基于现有管理员系统开发的专门用于管理运营的模块。该功能复用了现有的管理员表（`la_admin`），通过角色系统来区分运营身份，并实现了层级权限控制。

## 设计特点

- **复用现有表**：使用 `la_admin` 表存储运营基本信息
- **角色区分**：通过 `la_admin_role` 关联表，使用角色ID=4（运营角色）来标识运营
- **真实删除**：使用真实删除，删除后数据不可恢复
- **层级管理**：新增 `parent_id` 字段，支持管理员层级关系

## 文件结构

```
app/adminapi/
├── controller/auth/OperatorController.php     # 运营控制器
├── logic/auth/OperatorLogic.php              # 运营业务逻辑
├── lists/auth/OperatorLists.php              # 运营列表类
└── validate/auth/OperatorValidate.php        # 运营验证器

app/common/service/
└── TenantAdminService.php                    # 租户权限控制服务
```

## 核心功能模块

### 1. 数据管理 (OperatorLogic)
- **添加运营**：创建新的运营账号，自动分配运营角色
- **编辑运营**：修改运营基本信息，支持密码修改
- **删除运营**：真实删除运营数据，清理关联信息
- **查看详情**：获取运营详细信息

### 2. 列表展示 (OperatorLists)
- **分页列表**：支持分页显示运营列表
- **搜索筛选**：支持按姓名、账号搜索
- **排序功能**：支持按创建时间、ID排序
- **导出功能**：支持导出运营列表到Excel

### 3. 数据验证 (OperatorValidate)
- **基础验证**：账号、姓名、密码等基础字段验证
- **角色验证**：确保操作的对象是运营
- **唯一性验证**：账号唯一性检查

### 4. 权限控制 (TenantAdminService)
- **权限验证**：只有租户才能创建运营
- **层级查询**：递归查询所有下级管理员
- **关系验证**：防止循环引用和非法上级设置

### 5. API接口 (OperatorController)
- **RESTful设计**：标准的增删改查接口
- **权限集成**：集成层级权限控制
- **异常处理**：统一的错误处理机制

## 业务流程

### 运营创建流程
1. 验证操作者是否为租户
2. 验证运营基本信息
3. 设置上级关系（创建者为上级）
4. 创建运营账号
5. 分配运营角色
6. 清除权限缓存

### 运营编辑流程
1. 验证操作权限（只能编辑下级）
2. 验证修改信息
3. 更新运营数据
4. 处理密码修改
5. 处理禁用状态
6. 重新分配角色

### 运营删除流程
1. 验证操作权限
2. 检查是否有下级管理员
3. 真实删除运营数据
4. 清理角色关联
5. 清除登录会话和缓存

## 权限控制体系

### 创建权限
- **租户权限**：只有租户（角色ID=2）才能创建运营
- **root权限**：超级管理员也可以创建运营
- **层级关系**：自动将创建者设为新运营的上级

### 操作权限
- **层级控制**：管理员只能操作自己的下级运营
- **权限验证**：使用 `AdminHierarchyService::hasPermission()` 验证
- **可见范围**：使用 `AdminHierarchyService::getViewableAdminIds()` 控制

### 角色管理
- **角色分配**：自动分配运营角色（ID=4）
- **角色验证**：确保运营始终具有运营角色
- **角色一致性**：编辑时重新验证和分配角色

## 数据库设计

### 核心表结构
- **la_admin**：存储运营基本信息
- **la_admin_role**：关联运营角色（role_id=4）
- **la_system_role**：角色定义表

### 关键字段
- `parent_id`：上级管理员ID，实现层级关系
- `role_id`：通过关联表指向运营角色
- `disable`：启用/禁用状态
- `multipoint_login`：多点登录设置

## 技术实现

### 服务层设计
```php
// 租户权限验证
TenantAdminService::validateTenant($adminId, '创建运营');

// 层级权限验证
AdminHierarchyService::hasPermission($operatorId, $targetId);

// 获取可查看的管理员
AdminHierarchyService::getViewableAdminIds($adminId);
```

### 数据验证
```php
// 运营角色验证
$hasOperatorRole = AdminRole::where('admin_id', $value)
    ->where('role_id', 4) // 运营角色ID为4
    ->find();
```

### 事务处理
```php
Db::startTrans();
try {
    // 业务逻辑
    Db::commit();
    return true;
} catch (\Exception $e) {
    Db::rollback();
    self::setError($e->getMessage());
    return false;
}
```

## 使用方法

### 1. 创建运营
```php
// 控制器调用
$params = (new OperatorValidate())->post()->goCheck('add');
$result = OperatorLogic::add($params, $this->adminId);
```

### 2. 查询运营列表
```php
// 列表查询
return $this->dataLists(new OperatorLists());
```

### 3. 权限验证
```php
// 验证租户权限
TenantAdminService::validateTenant($this->adminId, '创建运营');
```

## 安全机制

### 1. 权限边界
- 严格按照层级权限控制，不能越权操作
- 租户只能创建和管理自己的下级运营
- root用户具有最高权限

### 2. 数据安全
- 真实删除不可恢复，操作需谨慎
- 删除前检查是否有下级管理员
- 自动清理相关缓存和会话

### 3. 角色一致性
- 确保运营始终具有运营角色
- 编辑时重新验证角色分配
- 防止角色混乱和权限泄露

## 功能清单

- ✅ 运营添加功能
- ✅ 运营编辑功能  
- ✅ 运营删除功能
- ✅ 运营详情查看
- ✅ 运营列表筛选
- ✅ 角色自动分配
- ✅ 不影响其他管理员功能
- ✅ 层级权限控制
- ✅ 自动上级设置
- ✅ 权限验证机制
- ✅ 真实删除功能
- ✅ 租户创建权限控制

## 注意事项

1. **权限边界**：严格按照层级权限控制，不能越权操作
2. **数据安全**：真实删除不可恢复，操作需谨慎
3. **角色一致性**：确保运营始终具有运营角色
4. **上级关系**：运营的上级关系一旦确定不可修改
5. **账号唯一性**：账号在全系统范围内必须唯一

## 技术要点

1. **复用设计**：最大化复用现有代码和数据结构
2. **权限集成**：无缝集成层级权限控制系统
3. **性能优化**：使用缓存提高权限查询效率
4. **异常处理**：完善的异常处理和错误提示
5. **代码规范**：遵循项目编码规范和注释要求

运营管理功能为系统提供了完整的运营管理能力，通过层级权限控制确保了数据安全和操作规范，是管理员层级权限系统的重要组成部分。
